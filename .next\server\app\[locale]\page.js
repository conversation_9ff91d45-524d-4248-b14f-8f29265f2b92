/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/page";
exports.ids = ["app/[locale]/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./es.json": [
		"(rsc)/./messages/es.json",
		"_rsc_messages_es_json"
	],
	"./hi.json": [
		"(rsc)/./messages/hi.json",
		"_rsc_messages_hi_json"
	],
	"./ja.json": [
		"(rsc)/./messages/ja.json",
		"_rsc_messages_ja_json"
	],
	"./pt.json": [
		"(rsc)/./messages/pt.json",
		"_rsc_messages_pt_json"
	],
	"./zh.json": [
		"(rsc)/./messages/zh.json",
		"_rsc_messages_zh_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/page.tsx */ \"(rsc)/./src/app/[locale]/page.tsx\")), \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\")), \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[locale]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/page\",\n        pathname: \"/[locale]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/header.tsx */ \"(ssr)/./src/components/layout/header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUN0YXJvdC1zZW8lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0LWludGwlNUMlNUNkaXN0JTVDJTVDZXNtJTVDJTVDc2hhcmVkJTVDJTVDTmV4dEludGxDbGllbnRQcm92aWRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q3Rhcm90LXNlbyU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQlNUMlNUNoZWFkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIySGVhZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnUEFBc0o7QUFDdEo7QUFDQSxnTEFBbUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teXN0aWNhbC13ZWJzaXRlLz9hOTMzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkU6XFxcXHRhcm90LXNlb1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dC1pbnRsXFxcXGRpc3RcXFxcZXNtXFxcXHNoYXJlZFxcXFxOZXh0SW50bENsaWVudFByb3ZpZGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJIZWFkZXJcIl0gKi8gXCJFOlxcXFx0YXJvdC1zZW9cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0XFxcXGhlYWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUN0YXJvdC1zZW8lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0LWludGwlNUMlNUNkaXN0JTVDJTVDZXNtJTVDJTVDc2hhcmVkJTVDJTVDTmV4dEludGxDbGllbnRQcm92aWRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdQQUFzSiIsInNvdXJjZXMiOlsid2VicGFjazovL215c3RpY2FsLXdlYnNpdGUvPzRhNGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRTpcXFxcdGFyb3Qtc2VvXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0LWludGxcXFxcZGlzdFxcXFxlc21cXFxcc2hhcmVkXFxcXE5leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/theme-toggle */ \"(ssr)/./src/components/ui/theme-toggle.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\n\nfunction Header() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)(\"navigation\");\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const navigation = [\n        {\n            label: t(\"home\"),\n            href: \"/\"\n        },\n        {\n            label: t(\"tarot\"),\n            href: \"/tarot\",\n            children: [\n                {\n                    label: \"Tarot Reading\",\n                    href: \"/tarot/test\"\n                },\n                {\n                    label: \"Tarot Cards\",\n                    href: \"/tarot/cards\"\n                },\n                {\n                    label: \"Tarot Guide\",\n                    href: \"/tarot/guide\"\n                },\n                {\n                    label: \"Tarot History\",\n                    href: \"/tarot/history\"\n                }\n            ]\n        },\n        {\n            label: t(\"astrology\"),\n            href: \"/astrology\",\n            children: [\n                {\n                    label: \"Astrology Test\",\n                    href: \"/astrology/test\"\n                },\n                {\n                    label: \"Zodiac Signs\",\n                    href: \"/astrology/signs\"\n                },\n                {\n                    label: \"Compatibility\",\n                    href: \"/astrology/compatibility\"\n                },\n                {\n                    label: \"Horoscope\",\n                    href: \"/astrology/horoscope\"\n                }\n            ]\n        },\n        {\n            label: t(\"numerology\"),\n            href: \"/numerology\",\n            children: [\n                {\n                    label: \"Numerology Test\",\n                    href: \"/numerology/test\"\n                },\n                {\n                    label: \"Number Calculator\",\n                    href: \"/numerology/calculator\"\n                },\n                {\n                    label: \"Number Meanings\",\n                    href: \"/numerology/meanings\"\n                }\n            ]\n        },\n        {\n            label: t(\"blog\"),\n            href: \"/blog\"\n        }\n    ];\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n        setActiveDropdown(null);\n    };\n    const toggleDropdown = (label)=>{\n        setActiveDropdown(activeDropdown === label ? null : label);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 rounded-full bg-mystical-gradient flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"M\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-mystical text-xl font-bold bg-mystical-gradient bg-clip-text text-transparent\",\n                                    children: \"Mystical\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: item.children ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-1 text-foreground/80 hover:text-foreground transition-colors\",\n                                                onMouseEnter: ()=>setActiveDropdown(item.label),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"absolute top-full left-0 mt-2 w-48 bg-card border border-border rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\", \"before:absolute before:-top-2 before:left-0 before:w-full before:h-2\"),\n                                                onMouseLeave: ()=>setActiveDropdown(null),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"py-2\",\n                                                    children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: child.href,\n                                                            className: \"block px-4 py-2 text-sm text-foreground/80 hover:text-foreground hover:bg-accent/50 transition-colors\",\n                                                            children: child.label\n                                                        }, child.href, false, {\n                                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: \"text-foreground/80 hover:text-foreground transition-colors\",\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.label, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"mystical\",\n                                    size: \"sm\",\n                                    children: \"Start Free Test\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"md:hidden p-2\",\n                            onClick: toggleMenu,\n                            \"aria-label\": \"Toggle menu\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden border-t border-border/40 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: item.children ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center justify-between w-full px-4 py-2 text-left text-foreground/80 hover:text-foreground transition-colors\",\n                                                onClick: ()=>toggleDropdown(item.label),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4 transition-transform\", activeDropdown === item.label && \"rotate-180\")\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 23\n                                            }, this),\n                                            activeDropdown === item.label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pl-4 space-y-1\",\n                                                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: child.href,\n                                                        className: \"block px-4 py-2 text-sm text-foreground/60 hover:text-foreground transition-colors\",\n                                                        onClick: ()=>setIsMenuOpen(false),\n                                                        children: child.label\n                                                    }, child.href, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 29\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: \"block px-4 py-2 text-foreground/80 hover:text-foreground transition-colors\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 21\n                                    }, this)\n                                }, item.label, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 pt-4 space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"mystical\",\n                                        size: \"sm\",\n                                        className: \"w-full\",\n                                        children: \"Start Free Test\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            mystical: \"bg-mystical-gradient text-white hover:shadow-mystical transition-all duration-300\",\n            golden: \"bg-golden-gradient text-white hover:shadow-golden transition-all duration-300\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            xl: \"h-12 rounded-lg px-10 text-base\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, loading = false, leftIcon, rightIcon, children, disabled, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"mr-2 h-4 w-4 animate-spin\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 71,\n                columnNumber: 11\n            }, undefined),\n            !loading && leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: leftIcon\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 92,\n                columnNumber: 34\n            }, undefined),\n            children,\n            !loading && rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: rightIcon\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 94,\n                columnNumber: 35\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 64,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/theme-toggle.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/theme-toggle.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeToggle: () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeToggle auto */ \n\n\nfunction ThemeToggle() {\n    const [isDark, setIsDark] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        // 检查当前主题状态\n        const savedTheme = localStorage.getItem(\"theme\");\n        const shouldBeDark = savedTheme === \"dark\";\n        setIsDark(shouldBeDark);\n        // 应用主题\n        if (shouldBeDark) {\n            document.documentElement.classList.add(\"dark\");\n        } else {\n            document.documentElement.classList.remove(\"dark\");\n        }\n    }, []);\n    const toggleTheme = ()=>{\n        const newIsDark = !isDark;\n        setIsDark(newIsDark);\n        // 更新DOM和本地存储\n        if (newIsDark) {\n            document.documentElement.classList.add(\"dark\");\n            localStorage.setItem(\"theme\", \"dark\");\n        } else {\n            document.documentElement.classList.remove(\"dark\");\n            localStorage.setItem(\"theme\", \"light\");\n        }\n    };\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-9 w-9 inline-flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"h-5 w-5 text-foreground-muted\"\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\theme-toggle.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\theme-toggle.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"h-9 w-9 inline-flex items-center justify-center rounded-md transition-colors\",\n        \"aria-label\": `Switch to ${isDark ? \"light\" : \"dark\"} mode`,\n        title: `Switch to ${isDark ? \"light\" : \"dark\"} mode`,\n        children: [\n            isDark ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-5 w-5 text-foreground\"\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\theme-toggle.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"h-5 w-5 text-foreground\"\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\theme-toggle.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Toggle theme\"\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\theme-toggle.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\theme-toggle.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/theme-toggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getRelativeTime: () => (/* binding */ getRelativeTime),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * 合并Tailwind CSS类名\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * 格式化日期\n */ function formatDate(date, locale = \"en-US\", options = {}) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    };\n    return new Intl.DateTimeFormat(locale, {\n        ...defaultOptions,\n        ...options\n    }).format(dateObj);\n}\n/**\n * 生成SEO友好的URL slug\n */ function generateSlug(text) {\n    return text.toLowerCase().trim().replace(/[^\\w\\s-]/g, \"\") // 移除特殊字符\n    .replace(/[\\s_-]+/g, \"-\") // 替换空格和下划线为连字符\n    .replace(/^-+|-+$/g, \"\"); // 移除开头和结尾的连字符\n}\n/**\n * 截断文本\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).trim() + \"...\";\n}\n/**\n * 延迟函数\n */ function delay(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * 防抖函数\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * 节流函数\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * 生成随机ID\n */ function generateId(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * 验证邮箱格式\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * 获取相对时间\n */ function getRelativeTime(date, locale = \"en\") {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    const rtf = new Intl.RelativeTimeFormat(locale, {\n        numeric: \"auto\"\n    });\n    if (diffInSeconds < 60) {\n        return rtf.format(-diffInSeconds, \"second\");\n    } else if (diffInSeconds < 3600) {\n        return rtf.format(-Math.floor(diffInSeconds / 60), \"minute\");\n    } else if (diffInSeconds < 86400) {\n        return rtf.format(-Math.floor(diffInSeconds / 3600), \"hour\");\n    } else if (diffInSeconds < 2592000) {\n        return rtf.format(-Math.floor(diffInSeconds / 86400), \"day\");\n    } else if (diffInSeconds < 31536000) {\n        return rtf.format(-Math.floor(diffInSeconds / 2592000), \"month\");\n    } else {\n        return rtf.format(-Math.floor(diffInSeconds / 31536000), \"year\");\n    }\n}\n/**\n * 深度克隆对象\n */ function deepClone(obj) {\n    if (obj === null || typeof obj !== \"object\") return obj;\n    if (obj instanceof Date) return new Date(obj.getTime());\n    if (obj instanceof Array) return obj.map((item)=>deepClone(item));\n    if (typeof obj === \"object\") {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n/**\n * 格式化数字\n */ function formatNumber(number, locale = \"en-US\", options = {}) {\n    return new Intl.NumberFormat(locale, options).format(number);\n}\n/**\n * 检查是否为移动设备\n */ function isMobile() {\n    if (true) return false;\n    return window.innerWidth < 768;\n}\n/**\n * 获取设备类型\n */ function getDeviceType() {\n    if (true) return \"desktop\";\n    const width = window.innerWidth;\n    if (width < 768) return \"mobile\";\n    if (width < 1024) return \"tablet\";\n    return \"desktop\";\n}\n/**\n * 复制文本到剪贴板\n */ async function copyToClipboard(text) {\n    try {\n        if (navigator.clipboard && window.isSecureContext) {\n            await navigator.clipboard.writeText(text);\n            return true;\n        } else {\n            // 降级方案\n            const textArea = document.createElement(\"textarea\");\n            textArea.value = text;\n            textArea.style.position = \"fixed\";\n            textArea.style.left = \"-999999px\";\n            textArea.style.top = \"-999999px\";\n            document.body.appendChild(textArea);\n            textArea.focus();\n            textArea.select();\n            const result = document.execCommand(\"copy\");\n            textArea.remove();\n            return result;\n        }\n    } catch (error) {\n        console.error(\"Failed to copy text:\", error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e5b0b01b189d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXlzdGljYWwtd2Vic2l0ZS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/MTA2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU1YjBiMDFiMTg5ZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/header */ \"(rsc)/./src/components/layout/header.tsx\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/i18n */ \"(rsc)/./src/i18n.ts\");\n\n\n\n\n\n\nfunction generateStaticParams() {\n    return _i18n__WEBPACK_IMPORTED_MODULE_3__.locales.map((locale)=>({\n            locale\n        }));\n}\nasync function LocaleLayout({ children, params: { locale } }) {\n    // 验证语言参数\n    if (!_i18n__WEBPACK_IMPORTED_MODULE_3__.locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    // 获取消息\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        messages: messages,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/page.tsx":
/*!***********************************!*\
  !*** ./src/app/[locale]/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_seo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/seo */ \"(rsc)/./src/lib/seo.ts\");\n\n\n\n\n\nasync function generateMetadata({ params: { locale } }) {\n    return (0,_lib_seo__WEBPACK_IMPORTED_MODULE_3__.generateMetadata)({\n        title: \"Discover Your Mystical Code - Free AI-Powered Tests\",\n        description: \"Professional AI analysis for accurate insights into your personality and destiny through tarot, astrology, and numerology tests.\",\n        keywords: [\n            \"mystical tests\",\n            \"free tests\",\n            \"AI analysis\",\n            \"tarot\",\n            \"astrology\",\n            \"numerology\"\n        ],\n        locale,\n        type: \"website\"\n    });\n}\nfunction HomePage({ params: { locale } }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\"homepage\");\n    const featuredTests = [\n        {\n            title: \"Tarot Reading\",\n            description: \"Unlock the wisdom of the cards and discover insights about your past, present, and future.\",\n            href: \"/tarot/test\",\n            icon: \"\\uD83D\\uDD2E\"\n        },\n        {\n            title: \"Astrology Analysis\",\n            description: \"Explore the influence of celestial bodies on your personality and life path.\",\n            href: \"/astrology/test\",\n            icon: \"⭐\"\n        },\n        {\n            title: \"Numerology Calculator\",\n            description: \"Decode the power of numbers and reveal your life path through mystical mathematics.\",\n            href: \"/numerology/test\",\n            icon: \"\\uD83D\\uDD22\"\n        },\n        {\n            title: \"Crystal Energy\",\n            description: \"Discover which crystals resonate with your energy and can enhance your spiritual journey.\",\n            href: \"/crystal/test\",\n            icon: \"\\uD83D\\uDC8E\"\n        },\n        {\n            title: \"Palm Reading\",\n            description: \"Analyze the lines and shapes of your hands to understand your character and destiny.\",\n            href: \"/palmistry/test\",\n            icon: \"✋\"\n        },\n        {\n            title: \"Dream Interpretation\",\n            description: \"Uncover the hidden meanings in your dreams and their significance to your waking life.\",\n            href: \"/dreams/test\",\n            icon: \"\\uD83C\\uDF19\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative overflow-hidden bg-gradient-to-br from-background via-background-secondary to-background-tertiary\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 star-field opacity-20 dark:opacity-30\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative container-responsive py-24 lg:py-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-mystical-gradient animate-fade-in\",\n                                    children: t(\"title\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl text-foreground-secondary mb-8 animate-fade-up\",\n                                    children: t(\"subtitle\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"mystical\",\n                                    size: \"xl\",\n                                    className: \"animate-scale-in\",\n                                    children: t(\"cta\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-background-secondary/30 dark:bg-background-secondary/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-responsive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4 text-golden-gradient\",\n                                    children: t(\"featuredTests\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-foreground-secondary max-w-2xl mx-auto\",\n                                    children: \"Choose from our collection of mystical tests powered by advanced AI analysis\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: featuredTests.map((test, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    variant: \"mystical\",\n                                    hover: true,\n                                    className: \"animate-fade-up\",\n                                    style: {\n                                        animationDelay: `${index * 0.1}s`\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl mb-4\",\n                                                    children: test.icon\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: test.title\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    children: test.description\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full\",\n                                                children: \"Start Test\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, test.title, true, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-background\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-responsive\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-fade-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-mystical-gradient mb-2\",\n                                        children: \"100K+\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-foreground-secondary\",\n                                        children: t(\"stats.users\")\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-fade-up\",\n                                style: {\n                                    animationDelay: \"0.1s\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-golden-gradient mb-2\",\n                                        children: \"95%\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-foreground-secondary\",\n                                        children: t(\"stats.accuracy\")\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-fade-up\",\n                                style: {\n                                    animationDelay: \"0.2s\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-mystical-gradient mb-2\",\n                                        children: \"6\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-foreground-secondary\",\n                                        children: t(\"stats.languages\")\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gradient-to-r from-primary-500/10 to-accent-500/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-responsive text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4\",\n                            children: \"Ready to Discover Your Mystical Path?\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-foreground-secondary mb-8 max-w-2xl mx-auto\",\n                            children: \"Join thousands of users who have already unlocked their spiritual insights with our AI-powered mystical tests.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"mystical\",\n                            size: \"xl\",\n                            children: \"Start Your Journey\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Mystical Website - Free Tarot, Astrology & Numerology Tests\",\n    description: \"Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis. Accurate personality insights and life guidance.\",\n    keywords: \"mystical tests, free tests, AI analysis, tarot, astrology, numerology\",\n    authors: [\n        {\n            name: \"Mystical Website Team\"\n        }\n    ],\n    creator: \"Mystical Website\",\n    publisher: \"Mystical Website\",\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"http://localhost:3000\",\n        siteName: \"Mystical Website\",\n        title: \"Mystical Website - Free Tarot, Astrology & Numerology Tests\",\n        description: \"Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis.\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Mystical Website - Free Tarot, Astrology & Numerology Tests\",\n        description: \"Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis.\",\n        creator: \"@mystical_website\"\n    },\n    verification: {\n        google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,\n        other: {\n            \"msvalidate.01\": process.env.NEXT_PUBLIC_BING_SITE_VERIFICATION || \"\"\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\tarot-seo\src\components\layout\header.tsx#Header`);


/***/ }),

/***/ "(rsc)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            mystical: \"bg-mystical-gradient text-white hover:shadow-mystical transition-all duration-300\",\n            golden: \"bg-golden-gradient text-white hover:shadow-golden transition-all duration-300\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            xl: \"h-12 rounded-lg px-10 text-base\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, loading = false, leftIcon, rightIcon, children, disabled, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"mr-2 h-4 w-4 animate-spin\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 71,\n                columnNumber: 11\n            }, undefined),\n            !loading && leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: leftIcon\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 92,\n                columnNumber: 34\n            }, undefined),\n            children,\n            !loading && rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: rightIcon\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 94,\n                columnNumber: 35\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 64,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBK0I7QUFDYTtBQUNzQjtBQUVqQztBQUVqQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4QiwwUkFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxhQUFhO1lBQ2JDLFNBQVM7WUFDVEMsV0FBVztZQUNYQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxRQUFRO1FBQ1Y7UUFDQUMsTUFBTTtZQUNKUixTQUFTO1lBQ1RTLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmZCxTQUFTO1FBQ1RTLE1BQU07SUFDUjtBQUNGO0FBWUYsTUFBTU0sdUJBQVNyQiw2Q0FBZ0IsQ0FDN0IsQ0FDRSxFQUNFdUIsU0FBUyxFQUNUakIsT0FBTyxFQUNQUyxJQUFJLEVBQ0pTLFVBQVUsS0FBSyxFQUNmQyxVQUFVLEtBQUssRUFDZkMsUUFBUSxFQUNSQyxTQUFTLEVBQ1RDLFFBQVEsRUFDUkMsUUFBUSxFQUNSLEdBQUdDLE9BQ0osRUFDREM7SUFFQSxNQUFNQyxPQUFPUixVQUFVdkIsc0RBQUlBLEdBQUc7SUFFOUIscUJBQ0UsOERBQUMrQjtRQUNDVCxXQUFXcEIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU1M7WUFBTVE7UUFBVTtRQUN4RFEsS0FBS0E7UUFDTEYsVUFBVUEsWUFBWUo7UUFDckIsR0FBR0ssS0FBSzs7WUFFUkwseUJBQ0MsOERBQUNRO2dCQUNDVixXQUFVO2dCQUNWVyxPQUFNO2dCQUNOQyxNQUFLO2dCQUNMQyxTQUFROztrQ0FFUiw4REFBQ0M7d0JBQ0NkLFdBQVU7d0JBQ1ZlLElBQUc7d0JBQ0hDLElBQUc7d0JBQ0hDLEdBQUU7d0JBQ0ZDLFFBQU87d0JBQ1BDLGFBQVk7Ozs7OztrQ0FFZCw4REFBQ0M7d0JBQ0NwQixXQUFVO3dCQUNWWSxNQUFLO3dCQUNMUyxHQUFFOzs7Ozs7Ozs7Ozs7WUFJUCxDQUFDbkIsV0FBV0MsMEJBQVksOERBQUNtQjtnQkFBS3RCLFdBQVU7MEJBQVFHOzs7Ozs7WUFDaERFO1lBQ0EsQ0FBQ0gsV0FBV0UsMkJBQWEsOERBQUNrQjtnQkFBS3RCLFdBQVU7MEJBQVFJOzs7Ozs7Ozs7Ozs7QUFHeEQ7QUFHRk4sT0FBT3lCLFdBQVcsR0FBRztBQUVhIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXlzdGljYWwtd2Vic2l0ZS8uL3NyYy9jb21wb25lbnRzL3VpL2J1dHRvbi50c3g/NmEwYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBTbG90IH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXNsb3QnO1xuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gJ2NsYXNzLXZhcmlhbmNlLWF1dGhvcml0eSc7XG5cbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuXG5jb25zdCBidXR0b25WYXJpYW50cyA9IGN2YShcbiAgJ2lubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB3aGl0ZXNwYWNlLW5vd3JhcCByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW0gcmluZy1vZmZzZXQtYmFja2dyb3VuZCB0cmFuc2l0aW9uLWNvbG9ycyBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6cG9pbnRlci1ldmVudHMtbm9uZSBkaXNhYmxlZDpvcGFjaXR5LTUwJyxcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6ICdiZy1wcmltYXJ5IHRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kIGhvdmVyOmJnLXByaW1hcnkvOTAnLFxuICAgICAgICBkZXN0cnVjdGl2ZTogJ2JnLWRlc3RydWN0aXZlIHRleHQtZGVzdHJ1Y3RpdmUtZm9yZWdyb3VuZCBob3ZlcjpiZy1kZXN0cnVjdGl2ZS85MCcsXG4gICAgICAgIG91dGxpbmU6ICdib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmQnLFxuICAgICAgICBzZWNvbmRhcnk6ICdiZy1zZWNvbmRhcnkgdGV4dC1zZWNvbmRhcnktZm9yZWdyb3VuZCBob3ZlcjpiZy1zZWNvbmRhcnkvODAnLFxuICAgICAgICBnaG9zdDogJ2hvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kJyxcbiAgICAgICAgbGluazogJ3RleHQtcHJpbWFyeSB1bmRlcmxpbmUtb2Zmc2V0LTQgaG92ZXI6dW5kZXJsaW5lJyxcbiAgICAgICAgbXlzdGljYWw6ICdiZy1teXN0aWNhbC1ncmFkaWVudCB0ZXh0LXdoaXRlIGhvdmVyOnNoYWRvdy1teXN0aWNhbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAnLFxuICAgICAgICBnb2xkZW46ICdiZy1nb2xkZW4tZ3JhZGllbnQgdGV4dC13aGl0ZSBob3ZlcjpzaGFkb3ctZ29sZGVuIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCcsXG4gICAgICB9LFxuICAgICAgc2l6ZToge1xuICAgICAgICBkZWZhdWx0OiAnaC0xMCBweC00IHB5LTInLFxuICAgICAgICBzbTogJ2gtOSByb3VuZGVkLW1kIHB4LTMnLFxuICAgICAgICBsZzogJ2gtMTEgcm91bmRlZC1tZCBweC04JyxcbiAgICAgICAgeGw6ICdoLTEyIHJvdW5kZWQtbGcgcHgtMTAgdGV4dC1iYXNlJyxcbiAgICAgICAgaWNvbjogJ2gtMTAgdy0xMCcsXG4gICAgICB9LFxuICAgIH0sXG4gICAgZGVmYXVsdFZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiAnZGVmYXVsdCcsXG4gICAgICBzaXplOiAnZGVmYXVsdCcsXG4gICAgfSxcbiAgfVxuKTtcblxuZXhwb3J0IGludGVyZmFjZSBCdXR0b25Qcm9wc1xuICBleHRlbmRzIFJlYWN0LkJ1dHRvbkhUTUxBdHRyaWJ1dGVzPEhUTUxCdXR0b25FbGVtZW50PixcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGJ1dHRvblZhcmlhbnRzPiB7XG4gIGFzQ2hpbGQ/OiBib29sZWFuO1xuICBsb2FkaW5nPzogYm9vbGVhbjtcbiAgbGVmdEljb24/OiBSZWFjdC5SZWFjdE5vZGU7XG4gIHJpZ2h0SWNvbj86IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuY29uc3QgQnV0dG9uID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MQnV0dG9uRWxlbWVudCwgQnV0dG9uUHJvcHM+KFxuICAoXG4gICAge1xuICAgICAgY2xhc3NOYW1lLFxuICAgICAgdmFyaWFudCxcbiAgICAgIHNpemUsXG4gICAgICBhc0NoaWxkID0gZmFsc2UsXG4gICAgICBsb2FkaW5nID0gZmFsc2UsXG4gICAgICBsZWZ0SWNvbixcbiAgICAgIHJpZ2h0SWNvbixcbiAgICAgIGNoaWxkcmVuLFxuICAgICAgZGlzYWJsZWQsXG4gICAgICAuLi5wcm9wc1xuICAgIH0sXG4gICAgcmVmXG4gICkgPT4ge1xuICAgIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6ICdidXR0b24nO1xuICAgIFxuICAgIHJldHVybiAoXG4gICAgICA8Q29tcFxuICAgICAgICBjbGFzc05hbWU9e2NuKGJ1dHRvblZhcmlhbnRzKHsgdmFyaWFudCwgc2l6ZSwgY2xhc3NOYW1lIH0pKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIGRpc2FibGVkPXtkaXNhYmxlZCB8fCBsb2FkaW5nfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICA+XG4gICAgICAgIHtsb2FkaW5nICYmIChcbiAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTQgYW5pbWF0ZS1zcGluXCJcbiAgICAgICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxuICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGNpcmNsZVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJvcGFjaXR5LTI1XCJcbiAgICAgICAgICAgICAgY3g9XCIxMlwiXG4gICAgICAgICAgICAgIGN5PVwiMTJcIlxuICAgICAgICAgICAgICByPVwiMTBcIlxuICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjRcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9wYWNpdHktNzVcIlxuICAgICAgICAgICAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgZD1cIm00IDEyYTggOCAwIDAxOC04VjBDNS4zNzMgMCAwIDUuMzczIDAgMTJoNHptMiA1LjI5MUE3Ljk2MiA3Ljk2MiAwIDAxNCAxMkgwYzAgMy4wNDIgMS4xMzUgNS44MjQgMyA3LjkzOGwzLTIuNjQ3elwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvc3ZnPlxuICAgICAgICApfVxuICAgICAgICB7IWxvYWRpbmcgJiYgbGVmdEljb24gJiYgPHNwYW4gY2xhc3NOYW1lPVwibXItMlwiPntsZWZ0SWNvbn08L3NwYW4+fVxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIHshbG9hZGluZyAmJiByaWdodEljb24gJiYgPHNwYW4gY2xhc3NOYW1lPVwibWwtMlwiPntyaWdodEljb259PC9zcGFuPn1cbiAgICAgIDwvQ29tcD5cbiAgICApO1xuICB9XG4pO1xuXG5CdXR0b24uZGlzcGxheU5hbWUgPSAnQnV0dG9uJztcblxuZXhwb3J0IHsgQnV0dG9uLCBidXR0b25WYXJpYW50cyB9O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2xvdCIsImN2YSIsImNuIiwiYnV0dG9uVmFyaWFudHMiLCJ2YXJpYW50cyIsInZhcmlhbnQiLCJkZWZhdWx0IiwiZGVzdHJ1Y3RpdmUiLCJvdXRsaW5lIiwic2Vjb25kYXJ5IiwiZ2hvc3QiLCJsaW5rIiwibXlzdGljYWwiLCJnb2xkZW4iLCJzaXplIiwic20iLCJsZyIsInhsIiwiaWNvbiIsImRlZmF1bHRWYXJpYW50cyIsIkJ1dHRvbiIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJhc0NoaWxkIiwibG9hZGluZyIsImxlZnRJY29uIiwicmlnaHRJY29uIiwiY2hpbGRyZW4iLCJkaXNhYmxlZCIsInByb3BzIiwicmVmIiwiQ29tcCIsInN2ZyIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJjaXJjbGUiLCJjeCIsImN5IiwiciIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwicGF0aCIsImQiLCJzcGFuIiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle),\n/* harmony export */   cardVariants: () => (/* binding */ cardVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\nconst cardVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", {\n    variants: {\n        variant: {\n            default: \"border-border\",\n            mystical: \"border-primary/20 bg-gradient-to-br from-card to-card/80 shadow-mystical\",\n            golden: \"border-accent/20 bg-gradient-to-br from-card to-accent/5 shadow-golden\",\n            glass: \"border-white/10 bg-white/5 backdrop-blur-md\"\n        },\n        size: {\n            default: \"p-6\",\n            sm: \"p-4\",\n            lg: \"p-8\",\n            xl: \"p-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, hover = false, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(cardVariants({\n            variant,\n            size\n        }), hover && \"transition-all duration-300 hover:shadow-lg hover:-translate-y-1\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 55,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 82,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 94,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 102,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/i18n.ts":
/*!*********************!*\
  !*** ./src/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   formatConfig: () => (/* binding */ formatConfig),\n/* harmony export */   languageConfig: () => (/* binding */ languageConfig),\n/* harmony export */   locales: () => (/* binding */ locales),\n/* harmony export */   pathnames: () => (/* binding */ pathnames)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n\n\n// 第一阶段：核心市场语言\nconst locales = [\n    \"en\",\n    \"zh\",\n    \"es\",\n    \"pt\",\n    \"hi\",\n    \"ja\"\n];\n// 语言配置\nconst languageConfig = {\n    en: {\n        name: \"English\",\n        nativeName: \"English\",\n        direction: \"ltr\",\n        region: \"Global\",\n        population: \"Global market\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n    },\n    zh: {\n        name: \"Chinese\",\n        nativeName: \"中文\",\n        direction: \"ltr\",\n        region: \"China & Chinese communities\",\n        population: \"1.4 billion\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\"\n    },\n    es: {\n        name: \"Spanish\",\n        nativeName: \"Espa\\xf1ol\",\n        direction: \"ltr\",\n        region: \"Spain & Latin America\",\n        population: \"500 million\",\n        flag: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\"\n    },\n    pt: {\n        name: \"Portuguese\",\n        nativeName: \"Portugu\\xeas\",\n        direction: \"ltr\",\n        region: \"Brazil & Portuguese-speaking countries\",\n        population: \"260 million\",\n        flag: \"\\uD83C\\uDDE7\\uD83C\\uDDF7\"\n    },\n    hi: {\n        name: \"Hindi\",\n        nativeName: \"हिन्दी\",\n        direction: \"ltr\",\n        region: \"Northern India\",\n        population: \"600 million\",\n        flag: \"\\uD83C\\uDDEE\\uD83C\\uDDF3\"\n    },\n    ja: {\n        name: \"Japanese\",\n        nativeName: \"日本語\",\n        direction: \"ltr\",\n        region: \"Japan\",\n        population: \"125 million\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\"\n    }\n};\n// 默认语言\nconst defaultLocale = \"en\";\n// 语言检测配置\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ locale })=>{\n    // 验证传入的语言是否支持\n    if (!locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n    try {\n        const messages = (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default;\n        return {\n            messages,\n            timeZone: getTimeZoneForLocale(locale),\n            now: new Date()\n        };\n    } catch (error) {\n        console.error(`Failed to load messages for locale: ${locale}`, error);\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n}));\n// 根据语言获取时区\nfunction getTimeZoneForLocale(locale) {\n    const timezoneMap = {\n        en: \"America/New_York\",\n        zh: \"Asia/Shanghai\",\n        es: \"Europe/Madrid\",\n        pt: \"America/Sao_Paulo\",\n        hi: \"Asia/Kolkata\",\n        ja: \"Asia/Tokyo\"\n    };\n    return timezoneMap[locale] || \"UTC\";\n}\n// 语言路径配置\nconst pathnames = {\n    \"/\": \"/\",\n    \"/blog\": {\n        en: \"/blog\",\n        zh: \"/blog\",\n        es: \"/blog\",\n        pt: \"/blog\",\n        hi: \"/blog\",\n        ja: \"/blog\"\n    },\n    \"/tarot\": {\n        en: \"/tarot\",\n        zh: \"/tarot\",\n        es: \"/tarot\",\n        pt: \"/tarot\",\n        hi: \"/tarot\",\n        ja: \"/tarot\"\n    },\n    \"/astrology\": {\n        en: \"/astrology\",\n        zh: \"/astrology\",\n        es: \"/astrology\",\n        pt: \"/astrology\",\n        hi: \"/astrology\",\n        ja: \"/astrology\"\n    },\n    \"/numerology\": {\n        en: \"/numerology\",\n        zh: \"/numerology\",\n        es: \"/numerology\",\n        pt: \"/numerology\",\n        hi: \"/numerology\",\n        ja: \"/numerology\"\n    }\n};\n// 语言特定的格式化配置\nconst formatConfig = {\n    en: {\n        dateFormat: \"MM/dd/yyyy\",\n        timeFormat: \"12h\",\n        currency: \"USD\",\n        numberFormat: \"en-US\"\n    },\n    zh: {\n        dateFormat: \"yyyy年MM月dd日\",\n        timeFormat: \"24h\",\n        currency: \"CNY\",\n        numberFormat: \"zh-CN\"\n    },\n    es: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"24h\",\n        currency: \"EUR\",\n        numberFormat: \"es-ES\"\n    },\n    pt: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"24h\",\n        currency: \"BRL\",\n        numberFormat: \"pt-BR\"\n    },\n    hi: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"12h\",\n        currency: \"INR\",\n        numberFormat: \"hi-IN\"\n    },\n    ja: {\n        dateFormat: \"yyyy年MM月dd日\",\n        timeFormat: \"24h\",\n        currency: \"JPY\",\n        numberFormat: \"ja-JP\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/seo.ts":
/*!************************!*\
  !*** ./src/lib/seo.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateArticleStructuredData: () => (/* binding */ generateArticleStructuredData),\n/* harmony export */   generateBreadcrumbStructuredData: () => (/* binding */ generateBreadcrumbStructuredData),\n/* harmony export */   generateFAQStructuredData: () => (/* binding */ generateFAQStructuredData),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateOrganizationStructuredData: () => (/* binding */ generateOrganizationStructuredData),\n/* harmony export */   generateStructuredData: () => (/* binding */ generateStructuredData),\n/* harmony export */   generateTestStructuredData: () => (/* binding */ generateTestStructuredData),\n/* harmony export */   generateWebsiteStructuredData: () => (/* binding */ generateWebsiteStructuredData),\n/* harmony export */   optimizeDescription: () => (/* binding */ optimizeDescription),\n/* harmony export */   optimizeTitle: () => (/* binding */ optimizeTitle)\n/* harmony export */ });\n/**\n * 生成页面元数据\n */ function generateMetadata(config) {\n    const { title, description, keywords = [], image, url, type = \"website\", locale = \"en\", siteName = \"Mystical Website\", author, publishedTime, modifiedTime, section, tags = [] } = config;\n    const metadata = {\n        title,\n        description,\n        keywords: keywords.join(\", \"),\n        authors: author ? [\n            {\n                name: author\n            }\n        ] : undefined,\n        // Open Graph\n        openGraph: {\n            title,\n            description,\n            type,\n            locale,\n            siteName,\n            url,\n            images: image ? [\n                {\n                    url: image,\n                    alt: title\n                }\n            ] : undefined,\n            publishedTime,\n            modifiedTime,\n            section,\n            tags\n        },\n        // Twitter Card\n        twitter: {\n            card: \"summary_large_image\",\n            title,\n            description,\n            images: image ? [\n                image\n            ] : undefined,\n            creator: author ? `@${author}` : undefined\n        },\n        // 其他元数据\n        robots: {\n            index: true,\n            follow: true,\n            googleBot: {\n                index: true,\n                follow: true,\n                \"max-video-preview\": -1,\n                \"max-image-preview\": \"large\",\n                \"max-snippet\": -1\n            }\n        },\n        // 验证标签\n        verification: {\n            google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,\n            other: {\n                \"msvalidate.01\": process.env.NEXT_PUBLIC_BING_SITE_VERIFICATION || \"\"\n            }\n        }\n    };\n    return metadata;\n}\n/**\n * 生成结构化数据 (JSON-LD)\n */ function generateStructuredData(type, data) {\n    const baseStructure = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": type,\n        ...data\n    };\n    return JSON.stringify(baseStructure);\n}\n/**\n * 生成网站结构化数据\n */ function generateWebsiteStructuredData(locale = \"en\") {\n    return generateStructuredData(\"WebSite\", {\n        name: \"Mystical Website\",\n        description: \"Professional mystical platform offering free tarot, astrology, and numerology tests with AI analysis.\",\n        url: \"http://localhost:3000\",\n        inLanguage: locale,\n        potentialAction: {\n            \"@type\": \"SearchAction\",\n            target: `${\"http://localhost:3000\"}/search?q={search_term_string}`,\n            \"query-input\": \"required name=search_term_string\"\n        }\n    });\n}\n/**\n * 生成文章结构化数据\n */ function generateArticleStructuredData(article) {\n    return generateStructuredData(\"Article\", {\n        headline: article.title,\n        description: article.description,\n        author: {\n            \"@type\": \"Person\",\n            name: article.author\n        },\n        publisher: {\n            \"@type\": \"Organization\",\n            name: \"Mystical Website\",\n            logo: {\n                \"@type\": \"ImageObject\",\n                url: `${\"http://localhost:3000\"}/images/logo.png`\n            }\n        },\n        datePublished: article.publishedTime,\n        dateModified: article.modifiedTime || article.publishedTime,\n        image: article.image,\n        url: article.url,\n        mainEntityOfPage: article.url,\n        articleSection: article.category\n    });\n}\n/**\n * 生成产品结构化数据（用于测试页面）\n */ function generateTestStructuredData(test) {\n    return generateStructuredData(\"Product\", {\n        name: test.name,\n        description: test.description,\n        category: test.category,\n        url: test.url,\n        image: test.image,\n        brand: {\n            \"@type\": \"Brand\",\n            name: \"Mystical Website\"\n        },\n        offers: {\n            \"@type\": \"Offer\",\n            price: \"0\",\n            priceCurrency: \"USD\",\n            availability: \"https://schema.org/InStock\"\n        }\n    });\n}\n/**\n * 生成面包屑结构化数据\n */ function generateBreadcrumbStructuredData(breadcrumbs) {\n    return generateStructuredData(\"BreadcrumbList\", {\n        itemListElement: breadcrumbs.map((item, index)=>({\n                \"@type\": \"ListItem\",\n                position: index + 1,\n                name: item.name,\n                item: item.url\n            }))\n    });\n}\n/**\n * 生成FAQ结构化数据\n */ function generateFAQStructuredData(faqs) {\n    return generateStructuredData(\"FAQPage\", {\n        mainEntity: faqs.map((faq)=>({\n                \"@type\": \"Question\",\n                name: faq.question,\n                acceptedAnswer: {\n                    \"@type\": \"Answer\",\n                    text: faq.answer\n                }\n            }))\n    });\n}\n/**\n * 生成组织结构化数据\n */ function generateOrganizationStructuredData() {\n    return generateStructuredData(\"Organization\", {\n        name: \"Mystical Website\",\n        description: \"Professional mystical platform offering free tarot, astrology, and numerology tests.\",\n        url: \"http://localhost:3000\",\n        logo: `${\"http://localhost:3000\"}/images/logo.png`,\n        sameAs: [\n            \"https://twitter.com/mystical_website\",\n            \"https://facebook.com/mystical_website\",\n            \"https://instagram.com/mystical_website\"\n        ],\n        contactPoint: {\n            \"@type\": \"ContactPoint\",\n            contactType: \"Customer Service\",\n            email: \"<EMAIL>\"\n        }\n    });\n}\n/**\n * 优化标题长度\n */ function optimizeTitle(title, maxLength = 60) {\n    if (title.length <= maxLength) return title;\n    // 尝试在单词边界截断\n    const truncated = title.slice(0, maxLength);\n    const lastSpace = truncated.lastIndexOf(\" \");\n    if (lastSpace > maxLength * 0.8) {\n        return truncated.slice(0, lastSpace) + \"...\";\n    }\n    return truncated.slice(0, maxLength - 3) + \"...\";\n}\n/**\n * 优化描述长度\n */ function optimizeDescription(description, maxLength = 160) {\n    if (description.length <= maxLength) return description;\n    // 尝试在句子边界截断\n    const sentences = description.split(\". \");\n    let result = \"\";\n    for (const sentence of sentences){\n        if ((result + sentence + \". \").length <= maxLength) {\n            result += sentence + \". \";\n        } else {\n            break;\n        }\n    }\n    if (result.length > 0) {\n        return result.trim();\n    }\n    // 如果没有合适的句子边界，在单词边界截断\n    const truncated = description.slice(0, maxLength);\n    const lastSpace = truncated.lastIndexOf(\" \");\n    if (lastSpace > maxLength * 0.8) {\n        return truncated.slice(0, lastSpace) + \"...\";\n    }\n    return truncated.slice(0, maxLength - 3) + \"...\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/seo.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getRelativeTime: () => (/* binding */ getRelativeTime),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * 合并Tailwind CSS类名\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * 格式化日期\n */ function formatDate(date, locale = \"en-US\", options = {}) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    };\n    return new Intl.DateTimeFormat(locale, {\n        ...defaultOptions,\n        ...options\n    }).format(dateObj);\n}\n/**\n * 生成SEO友好的URL slug\n */ function generateSlug(text) {\n    return text.toLowerCase().trim().replace(/[^\\w\\s-]/g, \"\") // 移除特殊字符\n    .replace(/[\\s_-]+/g, \"-\") // 替换空格和下划线为连字符\n    .replace(/^-+|-+$/g, \"\"); // 移除开头和结尾的连字符\n}\n/**\n * 截断文本\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).trim() + \"...\";\n}\n/**\n * 延迟函数\n */ function delay(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * 防抖函数\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * 节流函数\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * 生成随机ID\n */ function generateId(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * 验证邮箱格式\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * 获取相对时间\n */ function getRelativeTime(date, locale = \"en\") {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    const rtf = new Intl.RelativeTimeFormat(locale, {\n        numeric: \"auto\"\n    });\n    if (diffInSeconds < 60) {\n        return rtf.format(-diffInSeconds, \"second\");\n    } else if (diffInSeconds < 3600) {\n        return rtf.format(-Math.floor(diffInSeconds / 60), \"minute\");\n    } else if (diffInSeconds < 86400) {\n        return rtf.format(-Math.floor(diffInSeconds / 3600), \"hour\");\n    } else if (diffInSeconds < 2592000) {\n        return rtf.format(-Math.floor(diffInSeconds / 86400), \"day\");\n    } else if (diffInSeconds < 31536000) {\n        return rtf.format(-Math.floor(diffInSeconds / 2592000), \"month\");\n    } else {\n        return rtf.format(-Math.floor(diffInSeconds / 31536000), \"year\");\n    }\n}\n/**\n * 深度克隆对象\n */ function deepClone(obj) {\n    if (obj === null || typeof obj !== \"object\") return obj;\n    if (obj instanceof Date) return new Date(obj.getTime());\n    if (obj instanceof Array) return obj.map((item)=>deepClone(item));\n    if (typeof obj === \"object\") {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n/**\n * 格式化数字\n */ function formatNumber(number, locale = \"en-US\", options = {}) {\n    return new Intl.NumberFormat(locale, options).format(number);\n}\n/**\n * 检查是否为移动设备\n */ function isMobile() {\n    if (true) return false;\n    return window.innerWidth < 768;\n}\n/**\n * 获取设备类型\n */ function getDeviceType() {\n    if (true) return \"desktop\";\n    const width = window.innerWidth;\n    if (width < 768) return \"mobile\";\n    if (width < 1024) return \"tablet\";\n    return \"desktop\";\n}\n/**\n * 复制文本到剪贴板\n */ async function copyToClipboard(text) {\n    try {\n        if (navigator.clipboard && window.isSecureContext) {\n            await navigator.clipboard.writeText(text);\n            return true;\n        } else {\n            // 降级方案\n            const textArea = document.createElement(\"textarea\");\n            textArea.value = text;\n            textArea.style.position = \"fixed\";\n            textArea.style.left = \"-999999px\";\n            textArea.style.top = \"-999999px\";\n            document.body.appendChild(textArea);\n            textArea.focus();\n            textArea.select();\n            const result = document.execCommand(\"copy\");\n            textArea.remove();\n            return result;\n        }\n    } catch (error) {\n        console.error(\"Failed to copy text:\", error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendors"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=ts&pageExtensions=tsx&pageExtensions=js&pageExtensions=jsx&pageExtensions=md&pageExtensions=mdx&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();