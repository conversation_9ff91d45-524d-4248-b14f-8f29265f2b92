/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["src/middleware"],{

/***/ "(middleware)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.json": "(middleware)/./messages/en.json",
	"./es.json": "(middleware)/./messages/es.json",
	"./hi.json": "(middleware)/./messages/hi.json",
	"./ja.json": "(middleware)/./messages/ja.json",
	"./pt.json": "(middleware)/./messages/pt.json",
	"./zh.json": "(middleware)/./messages/zh.json"
};

function webpackAsyncContext(req) {
	return Promise.resolve().then(() => {
		if(!__webpack_require__.o(map, req)) {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		}

		var id = map[req];
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(middleware)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "(middleware)/./node_modules/next/dist/build/webpack/loaders/next-middleware-loader.js?absolutePagePath=E%3A%5Ctarot-seo%5Csrc%5Cmiddleware.ts&page=%2Fsrc%2Fmiddleware&rootDir=E%3A%5Ctarot-seo&matchers=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-middleware-loader.js?absolutePagePath=E%3A%5Ctarot-seo%5Csrc%5Cmiddleware.ts&page=%2Fsrc%2Fmiddleware&rootDir=E%3A%5Ctarot-seo&matchers=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ nHandler)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_globals__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/globals */ \"(middleware)/./node_modules/next/dist/esm/server/web/globals.js\");\n/* harmony import */ var next_dist_server_web_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/web/adapter */ \"(middleware)/./node_modules/next/dist/esm/server/web/adapter.js\");\n/* harmony import */ var _src_middleware_ts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src/middleware.ts */ \"(middleware)/./src/middleware.ts\");\n\n\n// Import the userland code.\n\nconst mod = {\n    ..._src_middleware_ts__WEBPACK_IMPORTED_MODULE_2__\n};\nconst handler = mod.middleware || mod.default;\nconst page = \"/src/middleware\";\nif (typeof handler !== \"function\") {\n    throw new Error(`The Middleware \"${page}\" must export a \\`middleware\\` or a \\`default\\` function`);\n}\nfunction nHandler(opts) {\n    return (0,next_dist_server_web_adapter__WEBPACK_IMPORTED_MODULE_1__.adapter)({\n        ...opts,\n        page,\n        handler\n    });\n}\n\n//# sourceMappingURL=middleware.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1taWRkbGV3YXJlLWxvYWRlci5qcz9hYnNvbHV0ZVBhZ2VQYXRoPUUlM0ElNUN0YXJvdC1zZW8lNUNzcmMlNUNtaWRkbGV3YXJlLnRzJnBhZ2U9JTJGc3JjJTJGbWlkZGxld2FyZSZyb290RGlyPUUlM0ElNUN0YXJvdC1zZW8mbWF0Y2hlcnM9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBc0M7QUFDaUI7QUFDdkQ7QUFDNEM7QUFDNUM7QUFDQSxPQUFPLCtDQUFJO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsS0FBSztBQUM1QztBQUNlO0FBQ2YsV0FBVyxxRUFBTztBQUNsQjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz8wNTgwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBcIm5leHQvZGlzdC9zZXJ2ZXIvd2ViL2dsb2JhbHNcIjtcbmltcG9ydCB7IGFkYXB0ZXIgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci93ZWIvYWRhcHRlclwiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgX21vZCBmcm9tIFwiLi9zcmMvbWlkZGxld2FyZS50c1wiO1xuY29uc3QgbW9kID0ge1xuICAgIC4uLl9tb2Rcbn07XG5jb25zdCBoYW5kbGVyID0gbW9kLm1pZGRsZXdhcmUgfHwgbW9kLmRlZmF1bHQ7XG5jb25zdCBwYWdlID0gXCIvc3JjL21pZGRsZXdhcmVcIjtcbmlmICh0eXBlb2YgaGFuZGxlciAhPT0gXCJmdW5jdGlvblwiKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBUaGUgTWlkZGxld2FyZSBcIiR7cGFnZX1cIiBtdXN0IGV4cG9ydCBhIFxcYG1pZGRsZXdhcmVcXGAgb3IgYSBcXGBkZWZhdWx0XFxgIGZ1bmN0aW9uYCk7XG59XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBuSGFuZGxlcihvcHRzKSB7XG4gICAgcmV0dXJuIGFkYXB0ZXIoe1xuICAgICAgICAuLi5vcHRzLFxuICAgICAgICBwYWdlLFxuICAgICAgICBoYW5kbGVyXG4gICAgfSk7XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1pZGRsZXdhcmUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/build/webpack/loaders/next-middleware-loader.js?absolutePagePath=E%3A%5Ctarot-seo%5Csrc%5Cmiddleware.ts&page=%2Fsrc%2Fmiddleware&rootDir=E%3A%5Ctarot-seo&matchers=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(middleware)/./src/i18n.ts":
/*!*********************!*\
  !*** ./src/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   formatConfig: () => (/* binding */ formatConfig),\n/* harmony export */   languageConfig: () => (/* binding */ languageConfig),\n/* harmony export */   locales: () => (/* binding */ locales),\n/* harmony export */   pathnames: () => (/* binding */ pathnames)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(middleware)/./node_modules/next/dist/esm/api/navigation.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(middleware)/./node_modules/next-intl/dist/development/server.react-client.js\");\n\n\n// 第一阶段：核心市场语言\nconst locales = [\n    \"en\",\n    \"zh\",\n    \"es\",\n    \"pt\",\n    \"hi\",\n    \"ja\"\n];\n// 语言配置\nconst languageConfig = {\n    en: {\n        name: \"English\",\n        nativeName: \"English\",\n        direction: \"ltr\",\n        region: \"Global\",\n        population: \"Global market\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n    },\n    zh: {\n        name: \"Chinese\",\n        nativeName: \"中文\",\n        direction: \"ltr\",\n        region: \"China & Chinese communities\",\n        population: \"1.4 billion\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\"\n    },\n    es: {\n        name: \"Spanish\",\n        nativeName: \"Espa\\xf1ol\",\n        direction: \"ltr\",\n        region: \"Spain & Latin America\",\n        population: \"500 million\",\n        flag: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\"\n    },\n    pt: {\n        name: \"Portuguese\",\n        nativeName: \"Portugu\\xeas\",\n        direction: \"ltr\",\n        region: \"Brazil & Portuguese-speaking countries\",\n        population: \"260 million\",\n        flag: \"\\uD83C\\uDDE7\\uD83C\\uDDF7\"\n    },\n    hi: {\n        name: \"Hindi\",\n        nativeName: \"हिन्दी\",\n        direction: \"ltr\",\n        region: \"Northern India\",\n        population: \"600 million\",\n        flag: \"\\uD83C\\uDDEE\\uD83C\\uDDF3\"\n    },\n    ja: {\n        name: \"Japanese\",\n        nativeName: \"日本語\",\n        direction: \"ltr\",\n        region: \"Japan\",\n        population: \"125 million\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\"\n    }\n};\n// 默认语言\nconst defaultLocale = \"en\";\n// 语言检测配置\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__.getRequestConfig)(async ({ locale })=>{\n    // 验证传入的语言是否支持\n    if (!locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n    try {\n        const messages = (await __webpack_require__(\"(middleware)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default;\n        return {\n            messages,\n            timeZone: getTimeZoneForLocale(locale),\n            now: new Date()\n        };\n    } catch (error) {\n        console.error(`Failed to load messages for locale: ${locale}`, error);\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n}));\n// 根据语言获取时区\nfunction getTimeZoneForLocale(locale) {\n    const timezoneMap = {\n        en: \"America/New_York\",\n        zh: \"Asia/Shanghai\",\n        es: \"Europe/Madrid\",\n        pt: \"America/Sao_Paulo\",\n        hi: \"Asia/Kolkata\",\n        ja: \"Asia/Tokyo\"\n    };\n    return timezoneMap[locale] || \"UTC\";\n}\n// 语言路径配置\nconst pathnames = {\n    \"/\": \"/\",\n    \"/blog\": {\n        en: \"/blog\",\n        zh: \"/blog\",\n        es: \"/blog\",\n        pt: \"/blog\",\n        hi: \"/blog\",\n        ja: \"/blog\"\n    },\n    \"/tarot\": {\n        en: \"/tarot\",\n        zh: \"/tarot\",\n        es: \"/tarot\",\n        pt: \"/tarot\",\n        hi: \"/tarot\",\n        ja: \"/tarot\"\n    },\n    \"/astrology\": {\n        en: \"/astrology\",\n        zh: \"/astrology\",\n        es: \"/astrology\",\n        pt: \"/astrology\",\n        hi: \"/astrology\",\n        ja: \"/astrology\"\n    },\n    \"/numerology\": {\n        en: \"/numerology\",\n        zh: \"/numerology\",\n        es: \"/numerology\",\n        pt: \"/numerology\",\n        hi: \"/numerology\",\n        ja: \"/numerology\"\n    }\n};\n// 语言特定的格式化配置\nconst formatConfig = {\n    en: {\n        dateFormat: \"MM/dd/yyyy\",\n        timeFormat: \"12h\",\n        currency: \"USD\",\n        numberFormat: \"en-US\"\n    },\n    zh: {\n        dateFormat: \"yyyy年MM月dd日\",\n        timeFormat: \"24h\",\n        currency: \"CNY\",\n        numberFormat: \"zh-CN\"\n    },\n    es: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"24h\",\n        currency: \"EUR\",\n        numberFormat: \"es-ES\"\n    },\n    pt: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"24h\",\n        currency: \"BRL\",\n        numberFormat: \"pt-BR\"\n    },\n    hi: {\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"12h\",\n        currency: \"INR\",\n        numberFormat: \"hi-IN\"\n    },\n    ja: {\n        dateFormat: \"yyyy年MM月dd日\",\n        timeFormat: \"24h\",\n        currency: \"JPY\",\n        numberFormat: \"ja-JP\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/i18n.ts\n");

/***/ }),

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var next_intl_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl/middleware */ \"(middleware)/./node_modules/next-intl/dist/development/middleware.js\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./i18n */ \"(middleware)/./src/i18n.ts\");\n\n\n\n// 创建国际化中间件\nconst intlMiddleware = (0,next_intl_middleware__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n    locales: _i18n__WEBPACK_IMPORTED_MODULE_1__.locales,\n    defaultLocale: _i18n__WEBPACK_IMPORTED_MODULE_1__.defaultLocale,\n    localePrefix: \"always\"\n});\n// 安全头配置\nconst securityHeaders = {\n    \"X-DNS-Prefetch-Control\": \"on\",\n    \"X-XSS-Protection\": \"1; mode=block\",\n    \"X-Frame-Options\": \"SAMEORIGIN\",\n    \"X-Content-Type-Options\": \"nosniff\",\n    \"Referrer-Policy\": \"origin-when-cross-origin\",\n    \"Permissions-Policy\": \"camera=(), microphone=(), geolocation=()\"\n};\n// 需要保护的路径\nconst protectedPaths = [\n    \"/admin\"\n];\n// API路径\nconst apiPaths = [\n    \"/api\"\n];\n// 静态资源路径\nconst staticPaths = [\n    \"/_next\",\n    \"/favicon.ico\",\n    \"/robots.txt\",\n    \"/sitemap.xml\",\n    \"/images\",\n    \"/icons\",\n    \"/fonts\"\n];\nfunction middleware(request) {\n    const { pathname } = request.nextUrl;\n    // 跳过静态资源\n    if (staticPaths.some((path)=>pathname.startsWith(path))) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // API路由处理\n    if (apiPaths.some((path)=>pathname.startsWith(path))) {\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n        // 添加安全头\n        Object.entries(securityHeaders).forEach(([key, value])=>{\n            response.headers.set(key, value);\n        });\n        // API特定的安全头\n        response.headers.set(\"Access-Control-Allow-Origin\", \"http://localhost:3000\" || 0);\n        response.headers.set(\"Access-Control-Allow-Methods\", \"GET, POST, PUT, DELETE, OPTIONS\");\n        response.headers.set(\"Access-Control-Allow-Headers\", \"Content-Type, Authorization\");\n        // 处理预检请求\n        if (request.method === \"OPTIONS\") {\n            return new Response(null, {\n                status: 200,\n                headers: response.headers\n            });\n        }\n        return response;\n    }\n    // 管理员路径保护\n    if (protectedPaths.some((path)=>pathname.startsWith(path))) {\n        // 这里可以添加认证逻辑\n        // 暂时重定向到首页\n        const url = request.nextUrl.clone();\n        url.pathname = \"/\";\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(url);\n    }\n    // 处理国际化\n    const response = intlMiddleware(request);\n    // 添加安全头\n    Object.entries(securityHeaders).forEach(([key, value])=>{\n        response.headers.set(key, value);\n    });\n    // 添加CSP头\n    const cspHeader = [\n        \"default-src 'self'\",\n        \"script-src 'self' 'unsafe-eval' 'unsafe-inline' https://umami.mystical-website.com\",\n        \"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com\",\n        \"img-src 'self' data: https: blob:\",\n        \"font-src 'self' data: https://fonts.gstatic.com\",\n        \"connect-src 'self' https://api.mystical-website.com https://umami.mystical-website.com\",\n        \"media-src 'self'\",\n        \"object-src 'none'\",\n        \"base-uri 'self'\",\n        \"form-action 'self'\",\n        \"frame-ancestors 'none'\",\n        \"upgrade-insecure-requests\"\n    ].join(\"; \");\n    response.headers.set(\"Content-Security-Policy\", cspHeader);\n    // 添加HSTS头（仅在生产环境）\n    if (false) {}\n    return response;\n}\nconst config = {\n    // 匹配所有路径，除了以下路径：\n    // - api (API routes)\n    // - _next/static (static files)\n    // - _next/image (image optimization files)\n    // - favicon.ico (favicon file)\n    matcher: [\n        \"/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|images|icons|fonts).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ }),

/***/ "(middleware)/./messages/en.json":
/*!**************************!*\
  !*** ./messages/en.json ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"common":{"loading":"Loading...","error":"Error","success":"Success","cancel":"Cancel","confirm":"Confirm","save":"Save","edit":"Edit","delete":"Delete","share":"Share","copy":"Copy","close":"Close","next":"Next","previous":"Previous","home":"Home","about":"About","contact":"Contact","privacy":"Privacy Policy","terms":"Terms of Service"},"navigation":{"home":"Home","blog":"Blog","tarot":"Tarot","astrology":"Astrology","numerology":"Numerology","crystal":"Crystal","palmistry":"Palmistry","dreams":"Dreams","tests":"Tests","about":"About","contact":"Contact"},"homepage":{"title":"Discover Your Mystical Code","subtitle":"Professional AI analysis for accurate insights into your personality and destiny","cta":"Start Free Test","featuredTests":"Featured Tests","testimonials":"What Our Users Say","recentActivity":"Recent Activity","stats":{"users":"users completed tests","accuracy":"accuracy rate","languages":"languages supported"}},"tarot":{"title":"Tarot Reading","subtitle":"Unlock the wisdom of the cards","description":"Discover insights about your past, present, and future through the ancient art of tarot reading.","startTest":"Start Tarot Reading","cards":"Tarot Cards","spreads":"Card Spreads","history":"Tarot History","guide":"Beginner\'s Guide"},"astrology":{"title":"Astrology","subtitle":"Explore the influence of celestial bodies","description":"Understand your personality traits and life path through the positions of stars and planets.","startTest":"Get Your Reading","signs":"Zodiac Signs","compatibility":"Compatibility","horoscope":"Daily Horoscope","birthChart":"Birth Chart"},"numerology":{"title":"Numerology","subtitle":"Decode the power of numbers","description":"Reveal your life path and personality through the mystical significance of numbers.","startTest":"Calculate Your Numbers","lifePathNumber":"Life Path Number","personalityNumber":"Personality Number","calculator":"Number Calculator","meanings":"Number Meanings"},"tests":{"title":"Mystical Tests","subtitle":"Discover your inner self","question":"Question","of":"of","next":"Next Question","previous":"Previous Question","submit":"Submit","result":"Your Result","shareResult":"Share Result","takeAnother":"Take Another Test","accuracy":"Accuracy","insights":"Key Insights"},"blog":{"title":"Mystical Blog","subtitle":"Explore the world of mysticism","readMore":"Read More","categories":"Categories","tags":"Tags","relatedPosts":"Related Posts","sharePost":"Share Post","comments":"Comments"},"footer":{"description":"Your trusted source for mystical insights and spiritual guidance.","quickLinks":"Quick Links","categories":"Categories","support":"Support","followUs":"Follow Us","newsletter":"Newsletter","newsletterDescription":"Subscribe to get the latest mystical insights","subscribe":"Subscribe","copyright":"© 2024 Mystical Website. All rights reserved."},"seo":{"defaultTitle":"Mystical Website - Free Tarot, Astrology & Numerology Tests","defaultDescription":"Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis. Accurate personality insights and life guidance.","keywords":"mystical tests, free tests, AI analysis, tarot, astrology, numerology"}}');

/***/ }),

/***/ "(middleware)/./messages/es.json":
/*!**************************!*\
  !*** ./messages/es.json ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"common":{"loading":"Cargando...","error":"Error","success":"Éxito","cancel":"Cancelar","confirm":"Confirmar","save":"Guardar","edit":"Editar","delete":"Eliminar","share":"Compartir","copy":"Copiar","close":"Cerrar","next":"Siguiente","previous":"Anterior","home":"Inicio","about":"Acerca de","contact":"Contacto","privacy":"Política de Privacidad","terms":"Términos de Servicio"},"navigation":{"home":"Inicio","blog":"Blog","tarot":"Tarot","astrology":"Astrología","numerology":"Numerología","crystal":"Cristales","palmistry":"Quiromancia","dreams":"Sueños","tests":"Pruebas","about":"Acerca de","contact":"Contacto"},"homepage":{"title":"Descubre Tu Código Místico","subtitle":"Análisis profesional con IA para conocer tu personalidad y destino","cta":"Comenzar Prueba Gratuita","featuredTests":"Pruebas Destacadas","testimonials":"Lo Que Dicen Nuestros Usuarios","recentActivity":"Actividad Reciente","stats":{"users":"usuarios completaron pruebas","accuracy":"tasa de precisión","languages":"idiomas soportados"}},"tarot":{"title":"Lectura de Tarot","subtitle":"Desbloquea la sabiduría de las cartas","description":"Descubre perspectivas sobre tu pasado, presente y futuro a través del arte ancestral del tarot.","startTest":"Comenzar Lectura de Tarot","cards":"Cartas de Tarot","spreads":"Tiradas de Cartas","history":"Historia del Tarot","guide":"Guía para Principiantes"},"seo":{"defaultTitle":"Sitio Místico - Pruebas Gratuitas de Tarot, Astrología y Numerología","defaultDescription":"Plataforma mística online profesional que ofrece pruebas gratuitas de tarot, astrología y numerología con análisis impulsado por IA.","keywords":"pruebas místicas, pruebas gratuitas, análisis IA, tarot, astrología, numerología"}}');

/***/ }),

/***/ "(middleware)/./messages/hi.json":
/*!**************************!*\
  !*** ./messages/hi.json ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"common":{"loading":"लोड हो रहा है...","error":"त्रुटि","success":"सफलता","cancel":"रद्द करें","confirm":"पुष्टि करें","save":"सहेजें","edit":"संपादित करें","delete":"हटाएं","share":"साझा करें","copy":"कॉपी करें","close":"बंद करें","next":"अगला","previous":"पिछला","home":"होम","about":"हमारे बारे में","contact":"संपर्क","privacy":"गोपनीयता नीति","terms":"सेवा की शर्तें"},"navigation":{"home":"होम","blog":"ब्लॉग","tarot":"टैरो","astrology":"ज्योतिष","numerology":"अंकशास्त्र","crystal":"क्रिस्टल","palmistry":"हस्तरेखा","dreams":"सपने","tests":"परीक्षण","about":"हमारे बारे में","contact":"संपर्क"},"homepage":{"title":"अपना रहस्यमय कोड खोजें","subtitle":"आपके व्यक्तित्व और भाग्य की सटीक जानकारी के लिए पेशेवर AI विश्लेषण","cta":"मुफ्त परीक्षण शुरू करें","featuredTests":"विशेष परीक्षण","testimonials":"हमारे उपयोगकर्ता क्या कहते हैं","recentActivity":"हाल की गतिविधि","stats":{"users":"उपयोगकर्ताओं ने परीक्षण पूरा किया","accuracy":"सटीकता दर","languages":"समर्थित भाषाएं"}},"seo":{"defaultTitle":"रहस्यमय वेबसाइट - मुफ्त टैरो, ज्योतिष और अंकशास्त्र परीक्षण","defaultDescription":"पेशेवर ऑनलाइन रहस्यमय प्लेटफॉर्म जो AI-संचालित विश्लेषण के साथ मुफ्त टैरो, ज्योतिष और अंकशास्त्र परीक्षण प्रदान करता है।","keywords":"रहस्यमय परीक्षण, मुफ्त परीक्षण, AI विश्लेषण, टैरो, ज्योतिष, अंकशास्त्र"}}');

/***/ }),

/***/ "(middleware)/./messages/ja.json":
/*!**************************!*\
  !*** ./messages/ja.json ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"common":{"loading":"読み込み中...","error":"エラー","success":"成功","cancel":"キャンセル","confirm":"確認","save":"保存","edit":"編集","delete":"削除","share":"共有","copy":"コピー","close":"閉じる","next":"次へ","previous":"前へ","home":"ホーム","about":"私たちについて","contact":"お問い合わせ","privacy":"プライバシーポリシー","terms":"利用規約"},"navigation":{"home":"ホーム","blog":"ブログ","tarot":"タロット","astrology":"占星術","numerology":"数秘術","crystal":"クリスタル","palmistry":"手相","dreams":"夢占い","tests":"テスト","about":"私たちについて","contact":"お問い合わせ"},"homepage":{"title":"あなたの神秘的なコードを発見","subtitle":"あなたの性格と運命を正確に読み解く専門的なAI分析","cta":"無料テストを開始","featuredTests":"注目のテスト","testimonials":"ユーザーの声","recentActivity":"最近のアクティビティ","stats":{"users":"人のユーザーがテストを完了","accuracy":"精度率","languages":"対応言語"}},"tarot":{"title":"タロット占い","subtitle":"カードの知恵を解き放つ","description":"古代のタロットの芸術を通じて、あなたの過去、現在、未来についての洞察を発見してください。","startTest":"タロット占いを開始","cards":"タロットカード","spreads":"カードスプレッド","history":"タロットの歴史","guide":"初心者ガイド"},"seo":{"defaultTitle":"神秘的なウェブサイト - 無料タロット、占星術、数秘術テスト","defaultDescription":"AI分析による無料のタロット、占星術、数秘術テストを提供する専門的なオンライン神秘プラットフォーム。","keywords":"神秘的なテスト, 無料テスト, AI分析, タロット, 占星術, 数秘術"}}');

/***/ }),

/***/ "(middleware)/./messages/pt.json":
/*!**************************!*\
  !*** ./messages/pt.json ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"common":{"loading":"Carregando...","error":"Erro","success":"Sucesso","cancel":"Cancelar","confirm":"Confirmar","save":"Salvar","edit":"Editar","delete":"Excluir","share":"Compartilhar","copy":"Copiar","close":"Fechar","next":"Próximo","previous":"Anterior","home":"Início","about":"Sobre","contact":"Contato","privacy":"Política de Privacidade","terms":"Termos de Serviço"},"navigation":{"home":"Início","blog":"Blog","tarot":"Tarô","astrology":"Astrologia","numerology":"Numerologia","crystal":"Cristais","palmistry":"Quiromancia","dreams":"Sonhos","tests":"Testes","about":"Sobre","contact":"Contato"},"homepage":{"title":"Descubra Seu Código Místico","subtitle":"Análise profissional com IA para insights precisos sobre sua personalidade e destino","cta":"Começar Teste Gratuito","featuredTests":"Testes em Destaque","testimonials":"O Que Nossos Usuários Dizem","recentActivity":"Atividade Recente","stats":{"users":"usuários completaram testes","accuracy":"taxa de precisão","languages":"idiomas suportados"}},"seo":{"defaultTitle":"Site Místico - Testes Gratuitos de Tarô, Astrologia e Numerologia","defaultDescription":"Plataforma mística online profissional oferecendo testes gratuitos de tarô, astrologia e numerologia com análise alimentada por IA.","keywords":"testes místicos, testes gratuitos, análise IA, tarô, astrologia, numerologia"}}');

/***/ }),

/***/ "(middleware)/./messages/zh.json":
/*!**************************!*\
  !*** ./messages/zh.json ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"common":{"loading":"加载中...","error":"错误","success":"成功","cancel":"取消","confirm":"确认","save":"保存","edit":"编辑","delete":"删除","share":"分享","copy":"复制","close":"关闭","next":"下一步","previous":"上一步","home":"首页","about":"关于我们","contact":"联系我们","privacy":"隐私政策","terms":"服务条款"},"navigation":{"home":"首页","blog":"博客","tarot":"塔罗牌","astrology":"星座","numerology":"数字命理","crystal":"水晶","palmistry":"手相","dreams":"解梦","tests":"测试","about":"关于","contact":"联系"},"homepage":{"title":"探索你的神秘密码","subtitle":"专业AI分析，准确解读你的性格与命运","cta":"开始免费测试","featuredTests":"精选测试","testimonials":"用户评价","recentActivity":"最近活动","stats":{"users":"位用户完成测试","accuracy":"准确率","languages":"种语言支持"}},"tarot":{"title":"塔罗牌占卜","subtitle":"解锁卡牌的智慧","description":"通过古老的塔罗牌艺术，发现关于你过去、现在和未来的洞察。","startTest":"开始塔罗占卜","cards":"塔罗牌","spreads":"牌阵","history":"塔罗历史","guide":"新手指南"},"astrology":{"title":"星座占星","subtitle":"探索天体的影响","description":"通过星星和行星的位置，了解你的性格特质和人生道路。","startTest":"获取你的解读","signs":"十二星座","compatibility":"星座配对","horoscope":"每日运势","birthChart":"星盘分析"},"numerology":{"title":"数字命理","subtitle":"解码数字的力量","description":"通过数字的神秘意义，揭示你的人生道路和性格。","startTest":"计算你的数字","lifePathNumber":"生命数字","personalityNumber":"性格数字","calculator":"数字计算器","meanings":"数字含义"},"tests":{"title":"神秘测试","subtitle":"发现你的内在自我","question":"问题","of":"共","next":"下一题","previous":"上一题","submit":"提交","result":"你的结果","shareResult":"分享结果","takeAnother":"再做一次测试","accuracy":"准确度","insights":"关键洞察"},"blog":{"title":"神秘博客","subtitle":"探索神秘学的世界","readMore":"阅读更多","categories":"分类","tags":"标签","relatedPosts":"相关文章","sharePost":"分享文章","comments":"评论"},"footer":{"description":"您值得信赖的神秘洞察和精神指导来源。","quickLinks":"快速链接","categories":"分类","support":"支持","followUs":"关注我们","newsletter":"订阅通讯","newsletterDescription":"订阅获取最新的神秘洞察","subscribe":"订阅","copyright":"© 2024 神秘网站。保留所有权利。"},"seo":{"defaultTitle":"神秘网站 - 免费塔罗牌、星座、数字命理测试","defaultDescription":"专业的在线神秘平台，提供免费塔罗牌、星座和数字命理测试，AI智能分析，准确的性格洞察和人生指导。","keywords":"神秘测试, 免费测试, AI分析, 塔罗牌, 星座, 数字命理"}}');

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors"], () => (__webpack_exec__("(middleware)/./node_modules/next/dist/build/webpack/loaders/next-middleware-loader.js?absolutePagePath=E%3A%5Ctarot-seo%5Csrc%5Cmiddleware.ts&page=%2Fsrc%2Fmiddleware&rootDir=E%3A%5Ctarot-seo&matchers=&preferredRegion=&middlewareConfig=e30%3D!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ (_ENTRIES = typeof _ENTRIES === "undefined" ? {} : _ENTRIES)["middleware_src/middleware"] = __webpack_exports__;
/******/ }
]);